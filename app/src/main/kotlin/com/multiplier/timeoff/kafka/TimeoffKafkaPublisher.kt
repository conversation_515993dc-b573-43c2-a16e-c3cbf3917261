package com.multiplier.timeoff.kafka

import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.config.EVENT_ID_KEY
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.kafka.proto.TimeoffEventMessageOuterClass
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.schema.GrpcTimeOffSession
import com.multiplier.timeoff.schema.GrpcTimeOffStatus
import com.multiplier.timeoff.service.TimeOffEntryService
import com.multiplier.timeoff.service.mapper.TimeoffMapper
import com.multiplier.timeoff.toDate
import com.multiplier.timeoff.types.TimeOff
import com.multiplier.timeoff.types.TimeOffStatus
import org.slf4j.MDC
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.support.SendResult
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.Map
import java.util.concurrent.CompletableFuture

@Component
class TimeoffKafkaPublisher(
    private val timeoffChangeKafkaTemplate: KafkaTemplate<String, TimeoffEventMessageOuterClass.TimeoffEventMessage>,
    private val featureFlagService: FeatureFlagService,
    private val timeOffEntryService: TimeOffEntryService,
    private val timeOffMapper: TimeoffMapper,
    private val contractServiceAdapter: ContractServiceAdapter
) {
    val log by logger()

    @Async
    fun publishTimeoffCreateEvent(timeoff: TimeOff) {
        if (isPublisherEnabled()) {
            sendKafkaMessage(createKafkaMessage(timeoff, TimeoffEventMessageOuterClass.TimeoffEventType.TIMEOFF_CREATED))
        } else {
            log.info("TimeoffChangeKafkaPublisher is disabled. Skipping timeoff create event for timeoffId={}", timeoff.id)
        }
    }

    @Async
    fun publishTimeoffUpdateEvent(timeoff: TimeOff) {
        if (isPublisherEnabled()) {
            sendKafkaMessage(createKafkaMessage(timeoff, TimeoffEventMessageOuterClass.TimeoffEventType.TIMEOFF_UPDATED))
        } else {
            log.info("TimeoffChangeKafkaPublisher is disabled. Skipping timeoff update event for timeoffId={}", timeoff.id)
        }
    }

    @Async
    fun publishTimeoffDeleteEvent(timeoff: TimeOff) {
        if (isPublisherEnabled()) {
            sendKafkaMessage(createKafkaMessage(timeoff, TimeoffEventMessageOuterClass.TimeoffEventType.TIMEOFF_DELETED))
        } else {
            log.info("TimeoffChangeKafkaPublisher is disabled. Skipping timeoff delete event for timeoffId={}", timeoff.id)
        }
    }

    private fun createKafkaMessage(
        timeoff: TimeOff,
        eventType: TimeoffEventMessageOuterClass.TimeoffEventType
    ): TimeoffEventMessageOuterClass.TimeoffEventMessage {
        val timeoffDBO = timeOffMapper.mapToDBO(timeoff)

        // Check if workshift integration is enabled for future leaves
        val contractBasicInfo = contractServiceAdapter.getBasicContractById(timeoff.contract.id);
        val isWorkshiftEnabled = isWorkshiftIntegrationEnabled(contractBasicInfo.companyId);

        // Use timeOffEntryService.getTimeOffEntries() to ensure gRPC TimeOffEntry objects
        // This works for both workshift-enabled and legacy flows
        val timeOffEntries = timeOffEntryService.getTimeOffEntries(timeOffs = listOf(timeoffDBO))

        return TimeoffEventMessageOuterClass.TimeoffEventMessage.newBuilder()
            .setEventType(eventType)
            .setEvent(
                TimeoffEventMessageOuterClass.TimeoffEvent.newBuilder()
                    .setTimeoffId(timeoff.id)
                    .setContractId(timeoff.contract.id)
                    .setTimeoffStatus(timeoff.status.toGrpc())
                    .setStartDate(timeoff.startDate.dateOnly.toDate())
                    .setEndDate(timeoff.endDate.dateOnly.toDate())
                    .setStartSession(GrpcTimeOffSession.valueOf(timeoff.startDate.session.name))
                    .setEndSession(GrpcTimeOffSession.valueOf(timeoff.endDate.session.name))
                    .setNoOfDays(timeoff.noOfDays)
                    .setIsPaidLeave(timeoff.type.isPaidLeave)
                    .addAllTimeoffEntries(timeOffEntries)
                    .build()
            )
            .build()
    }

    private fun sendKafkaMessage(eventMessage: TimeoffEventMessageOuterClass.TimeoffEventMessage) {
        try {
            val result = timeoffChangeKafkaTemplate.sendDefault(
                eventMessage.event.timeoffId.toString(),
                eventMessage
            )
            processResult(result)
        }
        catch (e: Exception) {
            log.error("Failed to send kafka message for event {} with timeoffID = {}", eventMessage.eventType, eventMessage.event.timeoffId, e)
        }
    }

    private fun <T> processResult(result: CompletableFuture<SendResult<String, T>>) {
        result.whenComplete { messageResult, exception ->
            messageResult.producerRecord.headers().lastHeader(EVENT_ID_KEY)?.let {
                MDC.put(EVENT_ID_KEY, String(it.value()))
            }

            if (exception != null) {
                log.error("Error sending timeoff approved message to kafka", exception)
            } else {
                log.info("Timeoff approve event sent to kafka")
            }

            MDC.remove(EVENT_ID_KEY)
        }
    }

    private fun isPublisherEnabled(): Boolean = featureFlagService.feature(FeatureFlags.TIME_OFF_PUBLISH_EVENT, mapOf()).on

    private fun isWorkshiftIntegrationEnabled(companyId: Long): Boolean {
        return featureFlagService.isOn(FeatureFlags.FUTURE_LEAVES, Map.of("company", companyId))
    }

    private fun TimeOffStatus.toGrpc(): GrpcTimeOffStatus = GrpcTimeOffStatus.valueOf(this.name)
}